import { prisma } from "@repo/db"
import { createTranslationTask } from "@/lib/services/BackgroundTaskService"
import { diff } from "@repo/utils"
import { ProjectGameStatus } from "@repo/shared-types"

/**
 * 翻译任务相关的接口定义
 */
export interface GameTranslationTaskParams {
	contentType: string
	contentId: string
	sourceLocale: string
	targetLocales: string[]
	fieldsToTranslate: string[]
	gameId: string
	gameName: string
}

export interface TranslationTaskInfo {
	contentId: string
	content: any
	operation: string
	originalContent: any
}

/**
 * 根据游戏内容类型获取需要翻译的字段
 */
export function getFieldsToTranslate(type: string): string[] {
	switch (type) {
		case "metadata":
			return [
				"title",
				"name",
				"description",
				"ogTitle",
				"ogDescription",
				"twitterTitle",
				"twitterDescription",
			]
		case "content":
			return ["title", "jsonContent", "text"]
		case "basicInfo":
			return ["gameName", "gameDescription", "name", "description"]
		default:
			// 对于未知类型，尝试翻译常见的文本字段
			return ["title", "name", "description", "content", "text"]
	}
}

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj: any, path: string): any {
	if (!obj || typeof obj !== "object") return undefined

	return path.split(".").reduce((current, key) => {
		return current?.[key] || undefined
	}, obj)
}

/**
 * 判断游戏内容是否需要创建翻译任务
 */
export async function shouldCreateGameTranslationTask(
	projectId: string,
	gameId: string,
	type: string,
	locale: string,
	newContent: any,
	existingContentId?: string,
): Promise<{ shouldTranslate: boolean; targetLocales: string[] }> {
	// 获取项目的语言设置
	const project = await prisma.project.findUnique({
		where: { id: projectId },
		include: {
			siteSettings: true,
		},
	})

	if (
		!project?.siteSettings ||
		!Array.isArray(project.siteSettings) ||
		project.siteSettings.length === 0
	) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	const siteSettings = project.siteSettings[0]
	if (!siteSettings) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	const languages = siteSettings.languanges as string[]
	const defaultLanguage = siteSettings.defaultLocale

	if (!languages || !Array.isArray(languages) || languages.length <= 1) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 如果当前语言不是默认语言，不创建翻译任务
	if (locale !== defaultLanguage) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 获取需要翻译的目标语言
	const targetLocales = languages.filter((lang) => lang !== defaultLanguage)
	if (targetLocales.length === 0) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 检查其他语言的内容是否为空
	const existingTranslations = await prisma.projectGameLocale.findMany({
		where: {
			projectId,
			gameId,
			type,
			locale: { in: targetLocales },
			...(existingContentId
				? {
						contentId: (
							await prisma.projectGameLocale.findUnique({
								where: { id: existingContentId },
								select: { contentId: true },
							})
						)?.contentId,
					}
				: {}),
		},
		select: { locale: true, content: true },
	})

	// 找出需要翻译的语言（内容为空或不存在的语言）
	const localesNeedingTranslation = targetLocales.filter((targetLocale) => {
		const existingTranslation = existingTranslations.find(
			(t) => t.locale === targetLocale,
		)
		if (!existingTranslation) return true

		// 检查是否有任何需要翻译的字段为空
		const fieldsToTranslate = getFieldsToTranslate(type)
		return fieldsToTranslate.some((field) => {
			const value = getNestedValue(existingTranslation.content, field)
			return !value || (typeof value === "string" && value.trim() === "")
		})
	})

	return {
		shouldTranslate: localesNeedingTranslation.length > 0,
		targetLocales: localesNeedingTranslation,
	}
}

/**
 * 创建单个游戏翻译任务
 */
export async function createSingleGameTranslationTask(
	type: string,
	projectId: string,
	userId: string,
	gameId: string,
	gameName: string,
	locale: string,
	content: any,
	existingContentId?: string,
): Promise<string | null> {
	try {
		const shouldTranslate = await shouldCreateGameTranslationTask(
			projectId,
			gameId,
			type,
			locale,
			content,
			existingContentId,
		)

		if (!shouldTranslate.shouldTranslate) {
			return null
		}

		const translationTaskId = await createTranslationTask(
			type,
			projectId,
			userId,
			{
				contentType: `ProjectGameLocale_${type}`,
				contentId: existingContentId || "",
				sourceLocale: locale,
				targetLocales: shouldTranslate.targetLocales,
				fieldsToTranslate: getFieldsToTranslate(type),
				gameId: gameId,
				gameName: gameName,
			},
		)

		return translationTaskId
	} catch (error) {
		console.error("创建翻译任务失败:", error)
		return null
	}
}

/**
 * 批量创建游戏翻译任务
 */
export async function createGameTranslationTasks(
	translationTasks: TranslationTaskInfo[],
	type: string,
	projectId: string,
	userId: string,
	gameId: string,
	gameName: string,
	locale: string,
): Promise<string[]> {
	const createdTranslationTasks: string[] = []

	if (translationTasks.length === 0) {
		return createdTranslationTasks
	}

	for (const task of translationTasks) {
		try {
			let shouldCreateTranslation = false

			if (task.operation === "create") {
				// 新创建的内容总是需要翻译
				shouldCreateTranslation = true
			} else if (task.operation === "update" && task.originalContent) {
				// 更新操作：使用diff比较内容是否有变化
				const fieldsToTranslate = getFieldsToTranslate(type)
				let hasChanges = false

				// 检查每个需要翻译的字段是否有变化
				for (const field of fieldsToTranslate) {
					const { hasDifferences } = diff(
						task.originalContent,
						task.content,
						field,
					)
					if (hasDifferences) {
						hasChanges = true
						break
					}
				}

				shouldCreateTranslation = hasChanges
			}

			if (shouldCreateTranslation) {
				const shouldTranslate = await shouldCreateGameTranslationTask(
					projectId,
					gameId,
					type,
					locale,
					task.content,
					task.operation === "update" ? task.contentId : undefined,
				)

				if (shouldTranslate.shouldTranslate) {
					const translationTaskId = await createTranslationTask(
						type,
						projectId,
						userId,
						{
							contentType: `ProjectGameLocale_${type}`,
							contentId: task.contentId,
							sourceLocale: locale,
							targetLocales: shouldTranslate.targetLocales,
							fieldsToTranslate: getFieldsToTranslate(type),
							gameId: gameId,
							gameName: gameName,
						},
					)
					createdTranslationTasks.push(translationTaskId)
				}
			}
		} catch (error) {
			console.error("创建翻译任务失败:", error)
			// 不影响主要的保存操作，只记录错误
		}
	}

	return createdTranslationTasks
}
