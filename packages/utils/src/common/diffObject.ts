import { isEqual } from "radash"
import { diffArray, type ArrayDiffResult } from "./diffArray"

/**
 * 对象差异对比结果接口
 */
export interface ObjectDiffResult {
  /** 新增的属性 */
  added: Record<string, any>
  /** 删除的属性 */
  removed: Record<string, any>
  /** 修改的属性 */
  modified: Record<string, {
    /** 原始值 */
    oldValue: any
    /** 新值 */
    newValue: any
    /** 属性路径 */
    path: string
  }>
  /** 未变化的属性 */
  unchanged: Record<string, any>
  /** 是否存在差异（新增、删除或修改） */
  hasDifferences: boolean
}

/**
 * 通用差异对比结果类型
 */
export type UniversalDiffResult<T = any> = T extends any[]
  ? ArrayDiffResult<T[number]>
  : T extends Record<string, any>
  ? ObjectDiffResult
  : {
      /** 原始值 */
      oldValue: T
      /** 新值 */
      newValue: T
      /** 是否存在差异 */
      hasDifferences: boolean
    }

/**
 * 对象差异对比选项
 */
export interface ObjectDiffOptions {
  /** 是否进行深度对比，默认为 true */
  deep?: boolean
  /** 要忽略的属性路径数组，支持深层路径语法（如 "a.b.c"） */
  ignorePaths?: string[]
  /** 要包含的属性路径数组，如果指定则只对比这些路径 */
  includePaths?: string[]
  /** 自定义比较函数，用于特定路径的值比较 */
  customComparers?: Record<string, (oldValue: any, newValue: any) => boolean>
}

/**
 * 检查路径是否应该被忽略
 * @param path 当前路径
 * @param ignorePaths 要忽略的路径数组
 * @returns 是否应该忽略
 */
function shouldIgnorePath(path: string, ignorePaths?: string[]): boolean {
  if (!ignorePaths || ignorePaths.length === 0) return false

  return ignorePaths.some(ignorePath => {
    // 支持通配符匹配
    if (ignorePath.includes("*")) {
      const regex = new RegExp(ignorePath.replace(/\*/g, ".*"))
      return regex.test(path)
    }
    // 精确匹配或前缀匹配
    return path === ignorePath || path.startsWith(`${ignorePath}.`)
  })
}

/**
 * 检查路径是否应该被包含
 * @param path 当前路径
 * @param includePaths 要包含的路径数组
 * @returns 是否应该包含
 */
function shouldIncludePath(path: string, includePaths?: string[]): boolean {
  if (!includePaths || includePaths.length === 0) return true

  return includePaths.some(includePath => {
    // 支持通配符匹配
    if (includePath.includes("*")) {
      const regex = new RegExp(includePath.replace(/\*/g, ".*"))
      return regex.test(path)
    }
    // 精确匹配或前缀匹配
    return path === includePath || path.startsWith(`${includePath}.`) || includePath.startsWith(`${path}.`)
  })
}

/**
 * 对比两个对象的差异，支持深度对比和自定义选项
 *
 * @param oldObject 原始对象
 * @param newObject 新对象
 * @param options 对比选项
 * @returns 差异结果，包含 hasDifferences 布尔值表示是否存在差异
 *
 * @example
 * ```typescript
 * // 基础对象对比
 * const obj1 = { a: 1, b: 2, c: 3 }
 * const obj2 = { a: 1, b: 3, d: 4 }
 * const result = diffObject(obj1, obj2)
 * // result.added: { d: 4 }
 * // result.removed: { c: 3 }
 * // result.modified: { b: { oldValue: 2, newValue: 3, path: "b" } }
 * // result.unchanged: { a: 1 }
 * // result.hasDifferences: true
 *
 * // 深层对象对比
 * const deep1 = { user: { name: "Alice", age: 25 }, settings: { theme: "dark" } }
 * const deep2 = { user: { name: "Alice", age: 26 }, settings: { theme: "light", lang: "en" } }
 * const deepResult = diffObject(deep1, deep2)
 * // deepResult.modified: { "user.age": { oldValue: 25, newValue: 26, path: "user.age" }, "settings.theme": { oldValue: "dark", newValue: "light", path: "settings.theme" } }
 * // deepResult.added: { "settings.lang": "en" }
 *
 * // 使用选项
 * const result2 = diffObject(obj1, obj2, {
 *   ignorePaths: ["c"], // 忽略 c 字段
 *   deep: false // 浅层对比
 * })
 *
 * // 自定义比较器
 * const result3 = diffObject(obj1, obj2, {
 *   customComparers: {
 *     "b": (oldVal, newVal) => Math.abs(oldVal - newVal) < 0.1 // 数值差异小于0.1视为相等
 *   }
 * })
 * ```
 */
export function diffObject(
  oldObject: Record<string, any>,
  newObject: Record<string, any>,
  options: ObjectDiffOptions = {}
): ObjectDiffResult {
  const {
    deep = true,
    ignorePaths = [],
    includePaths,
    customComparers = {}
  } = options

  // 边界情况处理
  if (!oldObject && !newObject) {
    return {
      added: {},
      removed: {},
      modified: {},
      unchanged: {},
      hasDifferences: false
    }
  }

  if (!oldObject) {
    return {
      added: { ...newObject },
      removed: {},
      modified: {},
      unchanged: {},
      hasDifferences: Object.keys(newObject || {}).length > 0
    }
  }

  if (!newObject) {
    return {
      added: {},
      removed: { ...oldObject },
      modified: {},
      unchanged: {},
      hasDifferences: Object.keys(oldObject).length > 0
    }
  }

  const result: ObjectDiffResult = {
    added: {},
    removed: {},
    modified: {},
    unchanged: {},
    hasDifferences: false
  }

  // 递归对比函数
  function compareObjects(
    oldObj: any,
    newObj: any,
    currentPath = ""
  ): void {
    // 获取所有键的并集
    const oldKeys = new Set(Object.keys(oldObj || {}))
    const newKeys = new Set(Object.keys(newObj || {}))
    const allKeys = new Set([...oldKeys, ...newKeys])

    for (const key of allKeys) {
      const fullPath = currentPath ? `${currentPath}.${key}` : key

      // 检查路径过滤
      if (shouldIgnorePath(fullPath, ignorePaths) || !shouldIncludePath(fullPath, includePaths)) {
        continue
      }

      const oldValue = oldObj?.[key]
      const newValue = newObj?.[key]
      const hasOldValue = oldKeys.has(key)
      const hasNewValue = newKeys.has(key)

      if (!hasOldValue && hasNewValue) {
        // 新增的属性
        result.added[fullPath] = newValue
      } else if (hasOldValue && !hasNewValue) {
        // 删除的属性
        result.removed[fullPath] = oldValue
      } else if (hasOldValue && hasNewValue) {
        // 存在于两个对象中的属性
        let valuesEqual = false

        // 使用自定义比较器（如果存在）
        if (customComparers[fullPath]) {
          valuesEqual = customComparers[fullPath](oldValue, newValue)
        } else {
          // 深度对比
          if (deep &&
              typeof oldValue === "object" && oldValue !== null &&
              typeof newValue === "object" && newValue !== null &&
              !Array.isArray(oldValue) && !Array.isArray(newValue)) {
            // 递归对比嵌套对象
            compareObjects(oldValue, newValue, fullPath)
            continue
          } else {
            // 使用 radash 的 isEqual 进行比较
            valuesEqual = isEqual(oldValue, newValue)
          }
        }

        if (valuesEqual) {
          result.unchanged[fullPath] = oldValue
        } else {
          result.modified[fullPath] = {
            oldValue,
            newValue,
            path: fullPath
          }
        }
      }
    }
  }

  // 开始对比
  compareObjects(oldObject, newObject)

  // 设置是否存在差异
  result.hasDifferences =
    Object.keys(result.added).length > 0 ||
    Object.keys(result.removed).length > 0 ||
    Object.keys(result.modified).length > 0

  return result
}

/**
 * 通用差异对比函数，根据输入类型自动选择合适的对比方式
 *
 * @param oldValue 原始值
 * @param newValue 新值
 * @param options 对比选项
 * @returns 根据输入类型返回相应的差异结果
 *
 * @example
 * ```typescript
 * // 对象对比
 * const objResult = diff({ a: 1 }, { a: 2, b: 3 })
 * // 返回 ObjectDiffResult
 *
 * // 数组对比
 * const arrResult = diff([1, 2, 3], [2, 3, 4])
 * // 返回 ArrayDiffResult
 *
 * // 基础类型对比
 * const primitiveResult = diff("hello", "world")
 * // 返回 { oldValue: "hello", newValue: "world", hasDifferences: true }
 *
 * // null/undefined 处理
 * const nullResult = diff(null, { a: 1 })
 * // 返回 ObjectDiffResult
 * ```
 */

// 函数重载声明
export function diff<T extends any[]>(
  oldValue: T | null | undefined,
  newValue: T | null | undefined,
  options?: { keyPath?: string | string[] }
): ArrayDiffResult<T[number]>

export function diff<T extends Record<string, any>>(
  oldValue: T | null | undefined,
  newValue: T | null | undefined,
  options?: ObjectDiffOptions
): ObjectDiffResult

export function diff<T>(
  oldValue: T | null | undefined,
  newValue: T | null | undefined,
  options?: any
): {
  oldValue: T | null | undefined
  newValue: T | null | undefined
  hasDifferences: boolean
}

// 函数实现
export function diff<T>(
  oldValue: T | null | undefined,
  newValue: T | null | undefined,
  options: any = {}
): any {
  // 处理 null 和 undefined 的情况
  const normalizedOldValue = oldValue ?? null
  const normalizedNewValue = newValue ?? null

  // 如果两个值都是 null/undefined，直接比较
  if (normalizedOldValue === null && normalizedNewValue === null) {
    return {
      oldValue: normalizedOldValue,
      newValue: normalizedNewValue,
      hasDifferences: false
    }
  }

  // 如果其中一个是 null/undefined，另一个不是
  if (normalizedOldValue === null || normalizedNewValue === null) {
    // 如果新值是数组，返回数组差异结果
    if (Array.isArray(normalizedNewValue)) {
      return diffArray([], normalizedNewValue, options.keyPath)
    }
    // 如果新值是对象，返回对象差异结果
    if (normalizedNewValue && typeof normalizedNewValue === "object") {
      return diffObject({}, normalizedNewValue, options)
    }
    // 如果旧值是数组，返回数组差异结果
    if (Array.isArray(normalizedOldValue)) {
      return diffArray(normalizedOldValue, [], options.keyPath)
    }
    // 如果旧值是对象，返回对象差异结果
    if (normalizedOldValue && typeof normalizedOldValue === "object") {
      return diffObject(normalizedOldValue, {}, options)
    }
    // 基础类型差异
    return {
      oldValue: normalizedOldValue,
      newValue: normalizedNewValue,
      hasDifferences: true
    }
  }

  // 两个值都不是 null/undefined，根据类型进行对比

  // 数组对比
  if (Array.isArray(normalizedOldValue) && Array.isArray(normalizedNewValue)) {
    return diffArray(normalizedOldValue, normalizedNewValue, options.keyPath)
  }

  // 对象对比
  if (
    typeof normalizedOldValue === "object" &&
    typeof normalizedNewValue === "object" &&
    !Array.isArray(normalizedOldValue) &&
    !Array.isArray(normalizedNewValue)
  ) {
    return diffObject(normalizedOldValue, normalizedNewValue, options)
  }

  // 基础类型对比
  const hasDifferences = !isEqual(normalizedOldValue, normalizedNewValue)
  return {
    oldValue: normalizedOldValue,
    newValue: normalizedNewValue,
    hasDifferences
  }
}
