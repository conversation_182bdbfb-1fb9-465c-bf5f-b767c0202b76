import { describe, expect, it } from "vitest"
import { diff } from "../diffObject"

describe("diff (通用差异对比函数)", () => {
  describe("对象对比", () => {
    it("应该正确对比对象", () => {
      const obj1 = { a: 1, b: 2 }
      const obj2 = { a: 1, b: 3, c: 4 }
      const result = diff(obj1, obj2)

      expect(result.added).toEqual({ c: 4 })
      expect(result.removed).toEqual({})
      expect(result.unchanged).toEqual({ a: 1 })
      expect(result.modified).toEqual({
        b: { oldValue: 2, newValue: 3, path: "b" }
      })
      expect(result.hasDifferences).toBe(true)
    })

    it("应该处理对象与 null 的对比", () => {
      const obj = { a: 1, b: 2 }
      const result1 = diff(obj, null)
      const result2 = diff(null, obj)

      expect(result1.added).toEqual({})
      expect(result1.removed).toEqual({ a: 1, b: 2 })
      expect(result1.hasDifferences).toBe(true)

      expect(result2.added).toEqual({ a: 1, b: 2 })
      expect(result2.removed).toEqual({})
      expect(result2.hasDifferences).toBe(true)
    })
  })

  describe("数组对比", () => {
    it("应该正确对比数组", () => {
      const arr1 = [1, 2, 3]
      const arr2 = [2, 3, 4]
      const result = diff(arr1, arr2)

      expect(result.added).toEqual([4])
      expect(result.removed).toEqual([1])
      expect(result.unchanged).toEqual([2, 3])
      expect(result.modified).toEqual([])
      expect(result.hasDifferences).toBe(true)
    })

    it("应该处理对象数组的对比", () => {
      const arr1 = [{ id: 1, name: "Alice" }, { id: 2, name: "Bob" }]
      const arr2 = [{ id: 1, name: "Alice Updated" }, { id: 3, name: "Charlie" }]
      const result = diff(arr1, arr2, { keyPath: "id" })

      expect(result.added).toEqual([{ id: 3, name: "Charlie" }])
      expect(result.removed).toEqual([{ id: 2, name: "Bob" }])
      expect(result.modified).toHaveLength(1)
      expect(result.modified[0].key).toBe(1)
      expect(result.hasDifferences).toBe(true)
    })

    it("应该处理数组与 null 的对比", () => {
      const arr = [1, 2, 3]
      const result1 = diff(arr, null)
      const result2 = diff(null, arr)

      expect(result1.added).toEqual([])
      expect(result1.removed).toEqual([1, 2, 3])
      expect(result1.hasDifferences).toBe(true)

      expect(result2.added).toEqual([1, 2, 3])
      expect(result2.removed).toEqual([])
      expect(result2.hasDifferences).toBe(true)
    })
  })

  describe("基础类型对比", () => {
    it("应该正确对比字符串", () => {
      const result1 = diff("hello", "world")
      const result2 = diff("same", "same")

      expect(result1.oldValue).toBe("hello")
      expect(result1.newValue).toBe("world")
      expect(result1.hasDifferences).toBe(true)

      expect(result2.oldValue).toBe("same")
      expect(result2.newValue).toBe("same")
      expect(result2.hasDifferences).toBe(false)
    })

    it("应该正确对比数字", () => {
      const result1 = diff(1, 2)
      const result2 = diff(5, 5)

      expect(result1.oldValue).toBe(1)
      expect(result1.newValue).toBe(2)
      expect(result1.hasDifferences).toBe(true)

      expect(result2.oldValue).toBe(5)
      expect(result2.newValue).toBe(5)
      expect(result2.hasDifferences).toBe(false)
    })

    it("应该正确对比布尔值", () => {
      const result1 = diff(true, false)
      const result2 = diff(false, false)

      expect(result1.oldValue).toBe(true)
      expect(result1.newValue).toBe(false)
      expect(result1.hasDifferences).toBe(true)

      expect(result2.oldValue).toBe(false)
      expect(result2.newValue).toBe(false)
      expect(result2.hasDifferences).toBe(false)
    })

    it("应该处理基础类型与 null 的对比", () => {
      const result1 = diff("hello", null)
      const result2 = diff(null, "world")
      const result3 = diff(42, undefined)

      expect(result1.oldValue).toBe("hello")
      expect(result1.newValue).toBe(null)
      expect(result1.hasDifferences).toBe(true)

      expect(result2.oldValue).toBe(null)
      expect(result2.newValue).toBe("world")
      expect(result2.hasDifferences).toBe(true)

      expect(result3.oldValue).toBe(42)
      expect(result3.newValue).toBe(null)
      expect(result3.hasDifferences).toBe(true)
    })
  })

  describe("null 和 undefined 处理", () => {
    it("应该正确处理 null 与 null 的对比", () => {
      const result = diff(null, null)

      expect(result.oldValue).toBe(null)
      expect(result.newValue).toBe(null)
      expect(result.hasDifferences).toBe(false)
    })

    it("应该正确处理 undefined 与 undefined 的对比", () => {
      const result = diff(undefined, undefined)

      expect(result.oldValue).toBe(null)
      expect(result.newValue).toBe(null)
      expect(result.hasDifferences).toBe(false)
    })

    it("应该正确处理 null 与 undefined 的对比", () => {
      const result = diff(null, undefined)

      expect(result.oldValue).toBe(null)
      expect(result.newValue).toBe(null)
      expect(result.hasDifferences).toBe(false)
    })
  })

  describe("混合类型对比", () => {
    it("应该处理不同类型之间的对比", () => {
      const result1 = diff([1, 2], { a: 1 })
      const result2 = diff("string", 123)
      const result3 = diff({ a: 1 }, [1, 2])

      expect(result1.oldValue).toEqual([1, 2])
      expect(result1.newValue).toEqual({ a: 1 })
      expect(result1.hasDifferences).toBe(true)

      expect(result2.oldValue).toBe("string")
      expect(result2.newValue).toBe(123)
      expect(result2.hasDifferences).toBe(true)

      expect(result3.oldValue).toEqual({ a: 1 })
      expect(result3.newValue).toEqual([1, 2])
      expect(result3.hasDifferences).toBe(true)
    })
  })

  describe("选项传递", () => {
    it("应该正确传递对象对比选项", () => {
      const obj1 = { a: 1, b: 2, c: 3 }
      const obj2 = { a: 1, b: 3, d: 4 }
      const result = diff(obj1, obj2, {
        ignorePaths: ["b"]
      })

      expect(result.added).toEqual({ d: 4 })
      expect(result.removed).toEqual({ c: 3 })
      expect(result.unchanged).toEqual({ a: 1 })
      expect(result.modified).toEqual({})
      expect(result.hasDifferences).toBe(true)
    })

    it("应该正确传递数组对比选项", () => {
      const arr1 = [{ type: "user", id: 1 }, { type: "admin", id: 1 }]
      const arr2 = [{ type: "user", id: 2 }, { type: "admin", id: 1 }]
      const result = diff(arr1, arr2, {
        keyPath: ["type", "id"]
      })

      expect(result.added).toEqual([{ type: "user", id: 2 }])
      expect(result.removed).toEqual([{ type: "user", id: 1 }])
      expect(result.unchanged).toEqual([{ type: "admin", id: 1 }])
      expect(result.hasDifferences).toBe(true)
    })
  })
})
